import request, { ResDataType } from '../../servicesV2/request';
import { API_ADSE, API_DEFAULT } from 'constantsV2/apiConfig';
import { QueryGoldCoinPageResponse, GoldCoinHistoryResponse, ExchangeResponse, CashFlowResponse } from './types';
import userInfoDetail from 'modulesV2/userInfoDetail';
import { encryptByType, getXuidTicket } from 'utilsV2/native';
import { XUID_ticketConfig } from 'constantsV2';
import uuid from 'utilsV2/uuid';
import { TransactionType } from 'constants/ad';
/**
 * 查询金币收支明细
 * @param transactionType 1-收入 2-支出 3-现金提现
 * @param curIndex 分页索引
 */
export const queryGoldCoinPage = async (
  transactionType: number = TransactionType.INCOME,
  curIndex?: string
): Promise<ResDataType<QueryGoldCoinPageResponse> | undefined> => {
  const params = new URLSearchParams();
  params.append('transactionType', String(transactionType));
  if (curIndex) {
    params.append('curIndex', curIndex);
  }

  return request<QueryGoldCoinPageResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryGoldCoinPage/ts-${Date.now()}?${params.toString()}&requestVersion=20250520`,
  });
};

/**
 * 查询金币历史记录
 * @param transactionType  1-收入 2-支出 3-现金提现
 * @param curIndex 分页索引
 * @param month 月份，格式：YYYY-MM
 */
export const queryGoldCoinHistory = async (
  transactionType: number = TransactionType.INCOME,
  curIndex?: string,
  month?: string
): Promise<ResDataType<GoldCoinHistoryResponse> | undefined> => {
  const params = new URLSearchParams();
  params.append('transactionType', String(transactionType));
  if (curIndex) {
    params.append('curIndex', curIndex);
  }
  if (month) {
    params.append('month', month);
  }

  return request<GoldCoinHistoryResponse>({
    ...API_ADSE,
    url: `incentive/ting/welfare/queryGoldCoinHistory/ts-${Date.now()}?${params.toString()}`,
  });
};

interface ExchangeParams {
  consumeType: number;
  coins: number;
  extMap?: string;
  retryCount?: number;
}

const MAX_RETRY_COUNT = 3;

/**
 * 兑换现金
 */
export const exchangeGoldCoin = async (params: ExchangeParams, retryCount = 0): Promise<ResDataType<ExchangeResponse>> => {
  const { consumeType, coins,  extMap = '' } = params;
  const uid = userInfoDetail.getDetail().uid || -1;
  const requestId = uuid();
  const ts = Date.now();
  const ticket = await getXuidTicket({
    businessId: XUID_ticketConfig.coinTask.businessId,
    scene: XUID_ticketConfig.coinTask.scene,
    uid,
  });

  const { checkData: signature } = await encryptByType('md5', {
    checkData: `${requestId}&${uid}&${consumeType}&${coins}&${ticket}&${ts}&${retryCount}&${XUID_ticketConfig.coinTask.salt}`
  });

  const data = {
    requestId,
    consumeType,
    coins,
    sourceName: 'GOLD_HISTORY_BANNER',
    ts,
    ticket,
    signature,
    retry: retryCount,
    ...(extMap && { extMap }),
  };

  try {
    const response = await request<ExchangeResponse>({
      ...API_ADSE,
      url: `incentive/ting/welfare/expendGoldCoin/ts-${ts}`,
      option: {
        method: 'post',
        data: JSON.stringify(data),
        headers: {
          'Content-Type': 'application/json',
        },
      }
    });

    if (response?.data?.retry && retryCount < MAX_RETRY_COUNT) {
      return exchangeGoldCoin(params, retryCount + 1);
    }

    return response as ResDataType<ExchangeResponse>;
  } catch (error) {
    if (retryCount < MAX_RETRY_COUNT) {
      return exchangeGoldCoin(params, retryCount + 1);
    }
    throw error;
  }
};

/**
 * 查询现金收支记录
 */
export const queryCashFlow = async (): Promise<ResDataType<CashFlowResponse> | undefined> => {
  return request<CashFlowResponse>({
    ...API_DEFAULT,
    url: 'point-http/account/cash/flow',
  });
};
