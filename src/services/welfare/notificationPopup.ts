
import { API_ADSE } from 'constantsV2/apiConfig';
import request, { ResDataType } from 'servicesV2/request';

export interface NotificationPopupConfig {
  success: boolean
  code: number
  title: string
  content: string
  color: string
}

export interface NotificationPopupResponse {
  responseId: number
  ret: number
  data: NotificationPopupConfig
}

// 本地存储的显示记录类型
export interface NotificationPopupShowRecord {
  date: string // 显示日期 (YYYY-MM-DD)
  shown: boolean // 是否已显示
}


/**
 * 获取通知弹窗配置
 */
export const fetchNotificationPopupConfig = async (): Promise<ResDataType<NotificationPopupResponse>> => {
  return request<NotificationPopupResponse>({
        ...API_ADSE,
        url: `incentive/ting/welfare/queryNotifyPopup/ts-${Date.now()}`,
      });
}
