import { atom } from 'jotai';
import { queryDrawInfo } from 'services/welfare';
interface WelfareState {
  coins: number;
  cash: number;
  enableCash: boolean;
  isLoading: boolean;
  coinTips: CoinTips;
  error: string | null;
}

interface CoinTips {
  icon: string;
  text: string;
}

interface TabsState {
  tabs: string[];
  activeTab?: 'COINS' | 'POINTS';
  isLoading: boolean;
  error: string | null;
}
const defaultCoinTips: CoinTips = {
  icon: 'https://imagev2.xmcdn.com/storages/23fc-audiofreehighqps/CD/85/GAqhqKwMIEn8AAADbwPGKWvE.png',
  text: '可兑换会员',
  // icon: 'https://imagev2.xmcdn.com/storages/79cf-audiofreehighqps/7B/C0/GAqhntAMIEn8AAADEgPGKWsR.png',
  // text: '瓜分奖励已到账',
};
const initialState: WelfareState = {
  coins: 0,
  cash: 0,
  enableCash: false,
  isLoading: false,
  error: null,
  coinTips: defaultCoinTips,
};

const initialTabsState: TabsState = {
  tabs: [],
  activeTab: undefined,
  isLoading: true,
  error: null,
};

export const balanceAtom = atom(initialState);
export const tabsAtom = atom(initialTabsState);

export const updateWelfareAtom = atom(
  (get) => ({ balance: get(balanceAtom), tabs: get(tabsAtom) }),
  async (get, set) => {
    const currentBalance = get(balanceAtom);
    const currentTabs = get(tabsAtom);
    
    set(balanceAtom, { ...currentBalance, isLoading: true, error: null });
    set(tabsAtom, { ...currentTabs, isLoading: true, error: null });

    try {
      const response = await queryDrawInfo();
      if (response && response.ret === 0 && response.data.success) {
        const { drawInfo, tabs, activateTab, coinTips } = response.data;
        if (drawInfo) {
          set(balanceAtom, {
            coins: drawInfo.coins,
            cash: drawInfo.cash,
            enableCash: drawInfo.enableCash,
            coinTips: coinTips,
            isLoading: false,
            error: null,
          });
          set(tabsAtom, {
            tabs,
            activeTab: activateTab,
            isLoading: false,
            error: null,
          });
        } else {
          throw new Error('No draw info available');
        }
      } else {
        throw new Error('Failed to fetch welfare info');
      }
    } catch (error) {
      console.log('debug_draw_info_error', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      set(balanceAtom, { ...currentBalance, isLoading: false, error: errorMessage });
      set(tabsAtom, { ...currentTabs, isLoading: false, error: errorMessage });
    }
  }
); 