import storage from '.'

const Key = 'drinkWaterMakeUpTime'

// 获取上次补打卡时间（时间戳，单位 ms）
const getDrinkWaterMakeUpTime = async (): Promise<number> => {
  try {
    const res = await storage.get(Key)
    const time = JSON.parse(res)
    if (!time) return 0
    return time
  } catch (error) {
    return 0
  }
}

// 设置本次补打卡时间（时间戳，单位 ms）
const setDrinkWaterMakeUpTime = async (time: number) => {
  await storage.set(Key, time)
}

export default {
  get: getDrinkWaterMakeUpTime,
  set: setDrinkWaterMakeUpTime,
} 