import { themeAtom } from 'atom/theme';
import { atom } from 'jotai';

export const darkTheme = {
  titleColor: '#ffffff',
  bgColor: '#000000',
};

export const lightTheme = {
  titleColor: '#000000',
  bgColor: '#FFFFFF'
};

export const drinkWaterHeaderThemeAtom = atom((get) => { 
  const theme = get(themeAtom);
  return theme === 'dark' ? darkTheme : lightTheme;
});

export default drinkWaterHeaderThemeAtom;
