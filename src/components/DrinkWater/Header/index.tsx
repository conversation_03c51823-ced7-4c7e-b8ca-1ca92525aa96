import React, { useContext } from 'react';
import { View, Text } from 'react-native';
import { getStyles } from './styles';
import BackBtn from 'componentsV2/common/BackBtn';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useAtomValue } from 'jotai';
import headerThemeAtom from './theme';
import { useNavigation } from '@react-navigation/native';
import { NativeInfoContext } from 'contextV2/nativeInfoContext';
import { waterInfoAtom } from '../EightGlasses/store';

export default function Header() {
  const theme = useAtomValue(headerThemeAtom);
  const styles = getStyles(theme);
  const insets = useSafeAreaInsets();
  const paddingTop = 10 + insets.top;
  const navigation = useNavigation();
  const nativeInfo = useContext(NativeInfoContext);
  const hideHeader = nativeInfo.embed === '1';
  const waterInfo = useAtomValue(waterInfoAtom);

  return (
    <View style={[styles.container, { paddingTop }, hideHeader ? { opacity: 0 } : null]}>
      <View style={[styles.backBtn, { top: paddingTop }]}>
        {hideHeader ? null : <BackBtn onPress={navigation.goBack}/>}
      </View>
      <Text style={styles.title}>{waterInfo?.title || '健康喝水打卡'}</Text>
    </View>
  );
}
