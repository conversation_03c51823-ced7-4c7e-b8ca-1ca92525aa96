import { StyleSheet } from 'react-native';
import { px } from 'utils/px';

export const getStyles = (_theme: 'light' | 'dark') => {
  return StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
    },
    modalContainer: {
      position: 'relative',
      width: '100%',
      height: '100%',
      justifyContent: 'center',
      alignItems: 'center',
    },
    closeButton: {
      position: 'absolute',
      right: px(16),
      top: px(44),
      padding: px(4),
      width: px(32),
      height: px(32),
      alignItems: 'center',
      justifyContent: 'center',
      zIndex: 100,
    },
  });
};
