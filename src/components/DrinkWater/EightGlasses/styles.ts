import { StyleSheet } from 'react-native';
import { px } from 'utils/px';

export const getStyles = () => {
  return StyleSheet.create({
    container: {
      marginHorizontal:px(16),
      marginTop: px(70),
      position: 'relative',
    },
    catImage: {
      width: px(79),
      height: px(55),
      resizeMode: "cover",
      zIndex: 2,
      position: 'absolute',
      right: 0,
      top: px(-31)
    },
    glassesContainer: {
      backgroundColor: '#FFFFFF',
      borderRadius: px(4),
      paddingHorizontal: px(12),
      paddingVertical: px(24),
      zIndex: 1
    },
    row: {
      flexDirection: 'row',
      marginBottom: px(16),
      justifyContent: 'flex-start',
    },
    lastRow: {
      marginBottom: px(0),
    },
    glassWrapper: {
      // 固定宽度，不使用 flex
    },
    glassMarginRight: {
      marginRight: px(12),
    },
    loadingContainer: {
      height: px(200),
      alignItems: 'center',
      justifyContent: 'center',
    },
    loadingText: {
      fontSize: px(14),
      color: '#999999',
    },
    errorContainer: {
      height: px(200),
      alignItems: 'center',
      justifyContent: 'center',
    },
    errorText: {
      fontSize: px(14),
      color: '#999999',
    },
  });
};
