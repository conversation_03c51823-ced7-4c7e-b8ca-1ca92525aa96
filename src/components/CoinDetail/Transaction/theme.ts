import { atom } from 'jotai';
import { themeAtom } from 'atom/theme';

export const darkTheme = {
  titleColor: 'rgba(255, 255, 255, 0.9)',
  valueColor: 'rgba(255, 255, 255, 0.55)',
  backgroundColor: '#1F1F1F',
  buttonColor: '#444444',
  icon: 'https://imagev2.xmcdn.com/storages/94a5-audiofreehighqps/02/B1/GAqh9sALtt1aAAAAzAOE4-Qc.png',
};

export const lightTheme = {
  titleColor: 'rgba(44, 44, 60, 0.9)',
  valueColor: 'rgba(44, 44, 60, 0.55)',
  backgroundColor: '#EAEEF6',
  buttonColor: '#FFFFFF',
  icon: 'https://imagev2.xmcdn.com/storages/7960-audiofreehighqps/39/DC/GKwRIDoLtt1aAAAA9QOE4-RN.png',
};

export const transactionThemeAtom = atom((get) => {
  const theme = get(themeAtom);
  //return theme === 'dark' ? lightTheme:darkTheme ;
  return theme === 'dark' ? darkTheme:lightTheme ;
});

export default transactionThemeAtom;