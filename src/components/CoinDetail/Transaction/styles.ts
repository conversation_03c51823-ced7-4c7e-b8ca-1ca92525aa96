import { StyleSheet, Platform } from 'react-native';
import { px } from 'utils/px';
import { darkTheme } from './theme';

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    // marginHorizontal: 16,
    marginBottom: px(20),
  },
  card: {
    backgroundColor: theme.backgroundColor,
    borderRadius: px(2),
    padding: px(12),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  title: {
    fontSize: px(14),
    color: theme.titleColor,
    fontFamily: 'XmlyNumber',
    //marginTop: px(12),
  },
  titleMargin: {
    marginTop: px(12),
  },
  subTitle: {
    margin: px(4),
  },
  valueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  value: {
    fontSize: px(10),
    color: theme.valueColor,
    fontFamily: 'XmlyNumber',
  },
  icon: {
    width: px(5),
    height: px(8),
    marginLeft: px(4),
  },
  cardrow: {
    flexDirection: 'row',
  },
  cardrowTwo: {
    flexDirection: 'row',
   justifyContent: 'center',
  },
  cardItem: {
    width: px(128),
    height: px(106),
    borderRadius: px(2),
    marginRight: px(8),
    backgroundColor: theme.backgroundColor,
    flexDirection: 'column',
    display: 'flex',
    alignItems: 'center',
  },
  cardItemFirst: {
    marginLeft: 16,
    // paddingLeft: 16,
  },
  cardItemLast: {
    marginRight: 16,
  },
  cardItemTwo: {
    width: px(168),
    height: px(106),
    borderRadius: px(2),
    margin: px(4),
    backgroundColor: theme.backgroundColor,
    flexDirection: 'column',
    display: 'flex',
    alignItems: 'center',
  },
  lineCoins: {
    textDecorationLine: 'line-through',  // 添加删除线
    textDecorationStyle: 'solid',
    marginRight: px(4),  // 与后面的文字保持一定间距
    color: "rgba(44, 44, 44, 0.3)",
  },
  lineCoinsDark: {
    color: 'rgba(255, 255, 255, 0.3)',
  },
  redNumber: {
    color: '#FF4444',  // 数字标红
  },
  normalText: {
    color: '#666666',  // 文字保持原有颜色
  },
  exchangeButton: {
    width: px(100),
    height: px(30),
    backgroundColor: theme.buttonColor,
    /* 自动布局 */
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: px(3),
    borderRadius: px(8),
    marginTop: 'auto',
    marginBottom: px(14),
  },
  exchangeButtonType1: {
    backgroundColor: '#F6D6DC',
  },
  exchangeButtonColor: {
    opacity: 1,
    backgroundColor: theme.buttonColor,
  },
  exchangeButtonDisabled: {
    // opacity: 0.3,  // 降低透明度
  },
  exchangeButtonText: { 
    fontFamily: 'PingFang SC',
    fontSize: px(11),
    fontWeight: '500',
    lineHeight: px(33),
    letterSpacing: px(0),
    color: theme.titleColor,
    textAlign: 'center',
    ...Platform.select({        // 添加平台特定样式
      ios: {
        marginTop: px(-4.5),    // iOS 下设置与按钮高度相同
        // 如果还不够居中，可以微调
        // paddingTop: px(2),
      },
      android: {
        textAlignVertical: 'center',
      },
    }),
  },
  exchangeButtonTextDisabled: {
    // color: '#999999',  // 置灰的文字颜色
    color: 'rgba(153, 153, 153, 0.6)',
  },
  exchangeButtonTextColor: {
    color: theme.titleColor,
  },
  exchangeButtonTextIOS: {
    marginTop: px(-4.5),    // iOS 下设置与按钮高度相同
  },
  statusImage: {
    position: 'absolute',
    right: 0,
    bottom: 0,
    width: px(48),
    height: px(48),
  }
  
}); 