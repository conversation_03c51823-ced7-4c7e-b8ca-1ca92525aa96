import { TransactionType } from 'constants/ad';
import { atom } from 'jotai';
import { queryGoldCoinPage, queryGoldCoinHistory, queryCashFlow } from 'services/welfare';
import { GoldCoinHistoryItem, BannerCard, CashFlowItem } from 'services/welfare/types';

interface CoinDetailState {
  pageLoading: boolean;
  loading: boolean;
  hasMore: boolean;
  curIndex: string;
  list: GoldCoinHistoryItem[];
  month?: string;
}

interface CashFlowState {
  loading: boolean;
  hasMore: boolean;
  curIndex: string;
  list: { title: string; delta: string; createTime: string; }[];
}

interface WithdrawState {
  loading: boolean;
  hasMore: boolean;
  curIndex: string;
  list: { title: string; delta: string; createTime: string; }[];
}

const initialState: CoinDetailState = {
  pageLoading: false,
  loading: false,
  hasMore: false,
  curIndex: '',
  list: [],
};

const initialCashFlowState: CashFlowState = {
  loading: false,
  hasMore: false,
  curIndex: '',
  list: [],
};

export const coinInfoAtom = atom<{ balance: number; bannerCards: BannerCard[]; showWithdraw: boolean }>({
  balance: 0,
  bannerCards: [],
  showWithdraw: false,
}); 

export const coinDetailAtom = atom({
  [TransactionType.INCOME]: { ...initialState },
  [TransactionType.EXPENSE]: { ...initialState },
  [TransactionType.WITHDRAW]: { ...initialState },
});

export const updateCoinDetailAtom = atom(
  null,
  async (get, set, params: { type: TransactionType, refresh?: boolean }) => {
    const { type, refresh } = params;
    const state = get(coinDetailAtom);
    const currentState = state[type];

    if (currentState.pageLoading) return;
    if (!refresh && !currentState.hasMore) return;

    const curIndex = refresh ? undefined : currentState.curIndex;

    try {
      set(coinDetailAtom, {
        ...state,
        [type]: { ...currentState, pageLoading: true },
      });

      const response = await queryGoldCoinPage(type, curIndex);

      if (response?.data?.success) {
        set(coinDetailAtom, {
          ...state,
          [type]: { ...currentState, pageLoading: false },
        });
        set(coinInfoAtom, {
          balance: response.data.coins,
          bannerCards: response.data.bannerCards,
          showWithdraw: response.data.enableWithDraw,
        });
      }
    } catch (error) {
      const state = get(coinDetailAtom);
      const currentState = state[type];
      set(coinDetailAtom, {
        ...state,
        [type]: { ...currentState, pageLoading: false },
      });
      console.error('Failed to fetch coin history:', error);
    }
  }
); 

export const updateDetailListAtom = atom(
  null,
  async (get, set, params: {
    type: TransactionType;
    month?: string;
    refresh?: boolean;
  }) => {
    const { type, month, refresh } = params;
    const state = get(coinDetailAtom);
    const currentState = state[type];
    
    if (currentState.loading) return;
    if (!refresh && !currentState.hasMore) return;

    const curIndex = refresh ? undefined : currentState.curIndex;
    console.log("type=" + type + ", month=" + month + ", refresh=" + refresh);
    console.log("currentState=", state);
    try {
      set(coinDetailAtom, {
        ...state,
        [type]: { ...currentState, loading: true },
      });
      const response = await queryGoldCoinHistory(type, curIndex, month);
      
      if (response?.data?.success) {
        const { list, hasMore, curIndex: newCurIndex } = response.data;

        const state = get(coinDetailAtom);
        const currentState = state[type];
        set(coinDetailAtom, {
          ...state,
          
          [type]: {
            ...currentState,
            loading: false,
            hasMore,
            curIndex: newCurIndex,
            list: refresh ? list : [...currentState.list, ...list],
            month,
          },
        });
      }
    } catch (error) {
      console.error('Failed to fetch coin history:', error);
      set(coinDetailAtom, {
        ...state,
        [type]: { ...currentState, loading: false },
      });
    }
  }
);
