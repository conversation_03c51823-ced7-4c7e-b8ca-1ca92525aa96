import React, { useState, useEffect, useRef } from 'react';
import { View, Text, TouchableOpacity, Animated } from 'react-native';
import { Touch, WheelPicker, BetterImage } from '@xmly/rn-components';
import dayjs from 'dayjs';
import { useAtomValue, useSetAtom } from 'jotai';
import tabsThemeAtom from './theme';
import { coinInfoAtom, updateDetailListAtom, coinDetailAtom } from '../atom';
import { TransactionType } from 'constants/ad';
import { getStyles } from './styles';
import IncomeRecords from '../IncomeRecords';
import ExpenseRecords from '../ExpenseRecords';
import WithdrawRecords from '../WithdrawRecords';
import Modal from './Modal';
import { px } from 'utils/px';

const allTabs = [
  { key: TransactionType.INCOME, title: '收入' },
  { key: TransactionType.EXPENSE, title: '支出' },
  { key: TransactionType.WITHDRAW, title: '提现' },
];

export default function Tabs() {
  const [activeTab, setActiveTab] = useState(TransactionType.INCOME);
  const theme = useAtomValue(tabsThemeAtom);
  const styles = getStyles(theme);
  const { showWithdraw } = useAtomValue(coinInfoAtom);
  const tabs = showWithdraw ? allTabs : allTabs.filter(tab => tab.key !== TransactionType.WITHDRAW);
  const updateDetailList = useSetAtom(updateDetailListAtom);

  const [showMonthPicker, setShowMonthPicker] = useState(false);
  const [selectedDate, setSelectedDate] = useState(dayjs());
  
  const now = dayjs();
  const currentYear = now.year();
  const currentMonth = now.month() + 1; // month() 从0开始

  // 计算最近12个月的年月
  const monthsList: { year: number; month: number }[] = [];
  for (let i = 0; i < 12; i++) {
    const date = now.subtract(i, 'month');
    monthsList.push({ year: date.year(), month: date.month() + 1 });
  }
  monthsList.reverse(); // 从旧到新

  // 年份列表
  const years = Array.from(new Set(monthsList.map(item => item.year)));

  // 月份列表根据当前选择的年份动态生成
  const getMonthsByYear = (year: number) =>
    monthsList.filter(item => item.year === year).map(item => item.month);

  // 选中年份和月份的初始值
  const [tempYear, setTempYear] = useState(currentYear);
  const [tempMonth, setTempMonth] = useState(currentMonth);

  // 年份选择处理
  const handleYearChange = ({ item: { value } }: { item: { value: number } }) => {
    setTempYear(value);
    // 切换年份时，自动选中该年可选的最大月份
    const months = getMonthsByYear(value);
    console.log('months', months);
    setTempMonth(months[months.length - 1]);
  };

  // 月份选择处理
  const handleMonthChange = ({ item: { value } }: { item: { value: number } }) => {
    setTempMonth(value);
  };

  // 确认按钮处理
  const handleConfirm = () => {
    // 确保年份不超过当前年份
    if (tempYear > currentYear) {
      setTempYear(currentYear);
    }
    
    const newDate = dayjs().year(tempYear).month(tempMonth - 1);
    setSelectedDate(newDate);
    setShowMonthPicker(false);
    
    updateDetailList({ 
      type: activeTab, 
      refresh: true,
      month: tempYear.toString() + '-' + tempMonth.toString()
    });
  };

  const slideAnim = useRef(new Animated.Value(0)).current;
  
  

  useEffect(() => {
    if (showMonthPicker) {
      Animated.spring(slideAnim, {
        toValue: 1,
        useNativeDriver: true,
        tension: 65,
        friction: 11,
      }).start();
    } else {
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }).start();
    }
  }, [showMonthPicker]);

  const renderMonthPicker = () => {
    if (!showMonthPicker) return null;

    return (
      <Modal visible={showMonthPicker} onClose={() => setShowMonthPicker(false)}>
        {renderAndroidMonthPicker()}
          <TouchableOpacity 
            style={styles.confirmButton}
            onPress={handleConfirm}
          >
            <Text style={styles.confirmButtonText}>确定</Text>
          </TouchableOpacity>
      </Modal>
    );
  };

  function renderAndroidMonthPicker() {
    return (
      <View style={styles.wheelPickerContainer}>
            <WheelPicker
              style={styles.yearWheelPicker}
              items={years.map(year => `${year}年`)}
              itemHeight={px(30)}
              offset={2}
              itemStyle={styles.yearItemTextStyle}
              selectedItemStyle={styles.yearOverlayItemStyle}
              selectedIndex={years.indexOf(tempYear)}
              onValueChange={(value, index) => handleYearChange({ item: { value: years[index] } })}
              // orientation={0}
            />
            <WheelPicker
              key={tempYear}
              style={styles.monthWheelPicker}
              items={getMonthsByYear(tempYear).map(month => `${month}月`)}
              itemHeight={px(30)}
              offset={2}
              itemStyle={styles.monthItemTextStyle}
              selectedItemStyle={styles.monthOverlayItemStyle}
              selectedIndex={getMonthsByYear(tempYear).indexOf(tempMonth)}
              onValueChange={(value, index) => handleMonthChange({ item: { value: getMonthsByYear(tempYear)[index] } })}
              // orientation={0}
            />
          </View>
    );
  }

  // function renderIOSMonthPicker() {
  //   return (<IOSMonthPickerModal
  //     styles={styles}
  //     selectedYear={tempYear}
  //     selectedMonth={tempMonth}
  //     onYearChange={(value) => handleYearChange({ item: { value } })}
  //     onMonthChange={(value) => handleMonthChange({ item: { value } })}
  //   />)
  // }

  function renderContent() {
    switch (activeTab) {
      case TransactionType.INCOME:
        return <IncomeRecords />;
      case TransactionType.EXPENSE:
        return <ExpenseRecords />;
      case TransactionType.WITHDRAW:
        return <WithdrawRecords />;
      default:
        return null;
    }
  }

  function handleTabChange(tab: TransactionType) {
    setActiveTab(tab);
    updateDetailList({ type: tab, refresh: true , month: tempYear.toString() + '-' + tempMonth.toString()});
  }

  useEffect(() => {
    // 加载当月数据
    const now = dayjs();
    setTimeout(() => {
      updateDetailList({
        type: activeTab,
        refresh: true,
        month: now.year().toString() + '-' + (now.month() + 1).toString()
      });
    }, 100);

  }, []);
  console.log("Tabs", useAtomValue(coinDetailAtom));
  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.tabs}>
          {tabs.map(tab => (
            <Touch
              key={tab.key}
              style={[styles.tab, activeTab === tab.key && styles.activeTab]}
              onPress={() => handleTabChange(tab.key)}
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Text style={[styles.tabText, activeTab === tab.key && styles.activeTabText]}>
                {tab.title}
              </Text>
              <View style={[styles.bottomLine, activeTab === tab.key && styles.activeBottomLine]} />
            </Touch>
          ))}
        </View>
        
        <TouchableOpacity 
          style={styles.monthSelector}
          onPress={() => setShowMonthPicker(true)}
        >
          <Text style={styles.monthSelectorText}>
            {selectedDate.format('YYYY.MM')}
          </Text>
          <BetterImage
              source={{ uri: theme.icon }}
              style={styles.icon}
              imgHeight={8}
              imgWidth={5}
            />
        </TouchableOpacity>
      </View>
      
      {renderContent()}
      {renderMonthPicker()}
    </View>
  );
} 