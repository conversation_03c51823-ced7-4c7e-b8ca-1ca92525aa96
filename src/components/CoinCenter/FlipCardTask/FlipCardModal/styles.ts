import { Platform, StyleSheet } from "react-native";
import { px } from "utils/px";
import { darkTheme } from "./theme";
const videoAspectRatio = 544 / 1200;

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'flex-end',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  content: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    zIndex: 2,
    top: '50%',
    left: '50%',
  },
  btns: {
    alignItems: 'center',
    justifyContent: 'center',
    position: 'absolute',
    zIndex: 2,
    top: '50%',
    left: '50%',
  },
  videoContainer: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  fullVideo: {
    ...StyleSheet.absoluteFillObject,
    width: '100%',
    height: '100%',
  },
  videoWrapper: {
    position: 'absolute',
    aspectRatio: videoAspectRatio,
    left: '50%',
    top: '50%',
  },
  topBg: {
    ...StyleSheet.absoluteFillObject,
    borderTopLeftRadius: px(12),
    borderTopRightRadius: px(12),
    height: px(60),
    position: 'absolute',
  },
  closeButton: {
    position: 'absolute',
    right: px(12),
    top: px(12),
    padding: px(4),
    width: px(20),
    height: px(20),
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeIcon: {
    width: px(24),
    height: px(24),
  },
  title: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  titleIcon: {
    width: px(99),
    aspectRatio: 99 / 28,
  },
  titleText: {
    fontFamily: 'XmlyNumber',
    fontSize: px(30),
    fontWeight: Platform.select({ ios: '500', android: 'bold' }),
    color: '#FFBABA',
    marginLeft: px(7),
  },
  cardNumber: {
    alignItems: 'center',
    justifyContent: 'center',
    top: '50%',
    position: 'absolute',
    left: 0,
  },
  cardNumberText: {
    fontSize: px(100),
    lineHeight: px(122),
    fontWeight: 'bold',
    color: '#D95757',
    fontFamily: 'XmlyNumber',
  },
  cardNumberTextK: {
    fontSize: px(55),
    lineHeight: px(67),
  },
  cardIcon: {
    width: px(71),
    aspectRatio: 71 / 42,
  },
  cardIconK: {
    aspectRatio: 64 / 38,
  },
  titleIconK: {
    aspectRatio: 72 / 29,
  },
  cardName: {
    fontFamily: 'XmlyNumber',
  },
  highlight: {
    fontFamily: 'XmlyNumber',
    fontWeight: '500',
    color: '#FF4444',
    marginRight: px(2),
  },
  retryButtonText: {
    borderRadius: px(120),
    height: px(44),
    width: px(219),
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonText: {
    fontSize: px(16),
    fontWeight: '600',
  },
  button: {
    color: '#FFF',
    fontSize: px(16),
    lineHeight: px(28),
  },
  ribbon: {
    width: px(90),
    height: px(120),
    position: 'absolute',
    top: px(12),
  },
  ribbonLeft: {
    left: px(30),
  },
  ribbonRight: {
    right: px(30),
  },
}); 