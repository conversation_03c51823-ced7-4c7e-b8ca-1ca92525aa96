import React  from "react";
import { View, Text, Image } from "react-native";
import Modal from "./Modal";

import { Touch } from "@xmly/rn-components";
import { getStyles } from "./styles";
import { useAtomValue } from "jotai";
import signInThemeStyleAtom from "./theme";
import LinearGradient from "react-native-linear-gradient";

import { bg_ribbon_left, bg_ribbon_right } from 'appImagesV2/bg_credit_reward';

import LottieView from 'lottie-react-native';


export default function PopWindow({ popVisible, rewardCoin, onClose }: {
  rewardCoin: number;
  popVisible: boolean,
  onClose: () => void,
}) {
  const theme = useAtomValue(signInThemeStyleAtom);
  const styles = getStyles(theme);
  const handleClose = () => {
    onClose();
  }
  return (<Modal
    visible={popVisible}
    overlayVisible={true}
    onClose={handleClose}
  >
    <View style={styles.rewardToastContent}>
      <Touch
        style={styles.closeButton}
        onPress={() => {

        }}
        hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
      >
        <Image
          source={theme.iconClose}
          height={16}
          width={16}
          style={styles.closeIcon}
        />
      </Touch>
      <LinearGradient
        colors={['rgba(226, 160, 160, 0.2)', 'rgba(226, 160, 160, 0)']}
        style={styles.windowGradient}
      />
      <LottieView
        source={bg_ribbon_left}
        autoPlay
        loop={false}
        style={styles.toastRibbonLeft}
      />
      <View style={styles.rewardToastTitleContainer}>
        <Text style={styles.rewardToastTitle}>
          恭喜获得
        </Text>
        <View style={styles.rewardToastSubTitleContainer}>
          <Text style={styles.rewardToastLeftSubTitle}>
            {rewardCoin}
          </Text>
          <Text style={styles.rewardToastRightSubTitle}>
            金币
          </Text>
        </View>
      </View>
      <LottieView
        source={bg_ribbon_right}
        autoPlay
        loop={false}
        style={styles.toastRibbonRight}
      />
      <Touch
        style={styles.rewardToastButton}
        onPress={() => onClose()}
      >
        <Text style={styles.rewardToastButtonText}>我知道了</Text>
      </Touch>
    </View>
  </Modal>
  );

}