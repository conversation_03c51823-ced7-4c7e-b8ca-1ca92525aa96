import { StyleSheet } from "react-native";
import { darkTheme } from "./theme";
import { px } from "utils/px";

export const getStyles = (theme: typeof darkTheme) => StyleSheet.create({
  modalContent: {
    padding: px(0),
    marginBottom: px(34),
    borderTopLeftRadius: px(12),
    borderTopRightRadius: px(12),
  },
  modalContentInitial: {
    width: 0,
    height: 0,
    opacity: 0,
  },
  modalContentVisible: {
    width: '100%',
    height: 'auto',
    opacity: 1,
  },
  modalHeader: {
    height: px(60),
    alignItems: 'center',
    justifyContent: 'flex-end',
    marginTop: px(0),
  },
  modalTitle: {
    fontSize: px(20),
    color: theme === darkTheme ? '#fff' : '#131313',
    fontWeight: 'bold',
  },
  coinText: {
    color: '#FF4444',
    fontWeight: 'bold',
  },
  modalSubTitle: {
    fontSize: px(14),
    color: '#666666',
    marginTop: px(8),
  },
  adContainer: {
    width: '100%',
    alignItems: 'center',
    position: 'relative',
    left: 0,
    top: 0,
    resizeMode: 'contain',
    opacity: 1,
    alignSelf: 'center',
    marginBottom: px(0),
  },
  adView: {
    width: '100%',
    flexDirection: 'column',
    height: '100%',
    display: 'flex',
    alignItems: 'center',
    alignSelf: 'center',
    justifyContent: 'center',
  },
  adRewardContainer: {
    position: 'relative',
    width: '100%',
    height: px(120),
    marginTop: px(10),
    marginBottom: px(20),
    zIndex: 2,
  },
  ribbonContainer: {
    position: 'relative',
    width: '100%',
    height: px(120),
    marginBottom: px(20),
  },
  rewardContainer: {
    position: 'relative',
    width: px(216),
    height: px(114),
    marginBottom: px(20),
    alignItems: 'center',
    alignSelf: 'center',
  },
  toastRibbonLeft: {
    position: 'absolute',
    left: px(0),
    top: px(0),
    width: px(90),
    height: px(120),
    zIndex: 0,
    // borderColor: 'red',
    // borderWidth: 1,
    // borderStyle: 'dashed',
  },
  toastRibbonRight: {
    position: 'absolute',
    right: px(0),
    top: px(0),
    width: px(90),
    height: px(120),
    zIndex: 0,
    // borderColor: 'red',
    // borderWidth: 1,
    // borderStyle: 'dashed',
  },
  ribbonLeft: {
    position: 'absolute',
    left: px(-5),
    top: px(-15),
    width: px(90),
    height: px(120),
    zIndex: 0,
  },
  ribbonRight: {
    position: 'absolute',
    right: px(-5),
    top: px(-15),
    width: px(90),
    height: px(120),
    zIndex: 0,
  },
  rewardBg: {
    position: 'absolute',
    width: px(216),
    height: px(113),
  },
  rewardTextContainer: {
    position: 'absolute',
    width: px(216),
    height: px(113),
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: px(30),
    paddingHorizontal: px(0),
    backgroundColor: 'transparent',
  },
  rewardItem: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'flex-end',
    flex: 1,
    position: 'relative',
    marginHorizontal: px(0),
    backgroundColor: 'transparent',
  },
  rewardText: {
    fontSize: px(14),
    color: '#333333',
    textAlign: 'center',
    fontWeight: 'bold',
  },
  rewardTextLeft: {
    fontSize: px(16),
    color: '#75919E',
    textAlign: 'center',
    fontWeight: 'bold',
    position: 'relative',
    marginBottom: px(0),
  },
  rewardTextRight: {
    fontSize: px(16),
    color: '#FF7B9C',
    textAlign: 'center',
    fontWeight: 'bold',
    position: 'relative',
    marginBottom: px(0),
  },
  rewardCoin: {
    fontSize: px(16),
    color: '#FF4444',
    fontWeight: 'bold',
    marginHorizontal: px(2),
    textAlign: 'center',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  closeButton: {
    position: 'absolute',
    top: px(12),
    right: px(12),
    width: px(16),
    height: px(16),
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 999,
    padding: px(2),
    backgroundColor: 'transparent',
  },
  closeIcon: {
    width: px(16),
    height: px(16),
  },
  rewardToastContent: {
    position: 'absolute',
    width: '100%',
    height: px(220),
    bottom: px(0),
    backgroundColor: theme === darkTheme ? '#1a1a1a' : '#fff',
    // borderRadius: px(8),
    borderTopLeftRadius: px(12),
    borderTopRightRadius: px(12),
    paddingTop: px(0),
    paddingHorizontal: px(0),
    alignItems: 'center',
  },
  windowGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: px(12),
    padding: px(0),
    height: '30%',
    margin: px(0),
  },
  rewardToastTitleContainer: {
    width: '100%',
    height: px(100),
    position: 'relative',
    marginTop: px(0),
    paddingTop: px(0),
    marginBottom: px(20),
  },
  rewardToastSubTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  rewardToastTitle: {
    fontSize: px(20),
    justifyContent: 'flex-end',
    color: theme === darkTheme ? '#fff' : '#131313',
    fontWeight: 'bold',
    textAlign: 'center',
    paddingTop: px(30),
    marginBottom: px(8),
  },
  rewardToastLeftSubTitle: {
    fontSize: px(30),
    color: '#FF4444',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: px(8),
  },
  rewardToastRightSubTitle: {
    fontSize: px(30),
    color: theme === darkTheme ? '#fff' : '#333333',
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: px(8),
  },
  rewardToastButton: {
    width: px(295),
    height: px(44),
    backgroundColor: '#FF4444',
    borderRadius: px(30),
    alignItems: 'center',
    marginTop: px(25),
    justifyContent: 'center',
  },
  rewardToastButtonText: {
    fontSize: px(16),
    color: '#FFFFFF',
    fontWeight: 'bold',
    height: px(44),
    lineHeight: px(44),
    textAlignVertical: 'center',
    textAlign: 'center',
  },
  rewardToastCoins: {
    fontSize: px(20),
    color: '#FF4444',
    fontWeight: 'bold',
    marginHorizontal: px(4),
  },
});