import React, { useEffect, useState } from "react";
import { View } from "react-native";
import { getStyles } from "./styles";
import ModuleCard from "../common/ModuleCard";
import HeaderTitle from "../common/HeaderTitle";
import { useAtomValue, useSetAtom } from "jotai";
import dailyTaskThemeAtom from "./theme";
import { dailyTaskAtom, updateDailyTaskAtom } from "./atom";
import DailyTaskItem from "./DailyTaskItem";
// import PopWindow from "./PopWindow";


export default function DailyTask({onRewardCoinFinish}:{onRewardCoinFinish:(coin:number)=>void}) {
  const styles = getStyles();
  const theme = useAtomValue(dailyTaskThemeAtom);
  const { list, countdowns, loading, updatingPositions } = useAtomValue(dailyTaskAtom);
  const updateDailyTask = useSetAtom(updateDailyTaskAtom);
  // const [popVisible, setPopVisible] = useState(false);
  // const [rewardCoin, setRewardCoin] =useState<number>(0);

  useEffect(() => {
    updateDailyTask();
  }, []);
// const onRewardCoinFinish = (coin:number)=>{
//   setRewardCoin(coin);
//   setPopVisible(true);
// }
  return (
    <>
    {/* <PopWindow rewardCoin={rewardCoin} popVisible={popVisible} onClose={() => setPopVisible(false)}/> */}
    <ModuleCard style={styles.container}>
      <HeaderTitle title="每日任务" titleIcon={theme.titleIcon} />
      <View style={styles.taskList}>
        {list.map((item, index) => {
          const key = `${item.positionId}${item.title}`;
          return (
            <DailyTaskItem
              index={index}
              key={key}
              item={item}
              countdown={countdowns[key] || 0}
              isFirst={index === 0}
              loading={loading}
              updatingPositions={updatingPositions}
              onRewardCoinFinish={onRewardCoinFinish}
            />
          )
        })}
      </View>
    </ModuleCard>
    </>
  );
} 