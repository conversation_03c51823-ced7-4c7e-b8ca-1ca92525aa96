import React, { useCallback, useEffect, useState } from 'react'
import { View, Text, Image, TouchableOpacity } from 'react-native'
import { Touch } from '@xmly/rn-components'
import { useAtomValue, useSetAtom } from 'jotai'
import Modal from './Modal/index'
import { getStyles } from './styles'
import { notificationPopupThemeAtom } from './theme'
import { parseContentWithHighlight, isValidConfig } from './utils'
import { setNotificationPopupShowRecord, hasShownTodayNotificationPopup } from './storage'
import { notificationPopupConfigAtom, writeNotificationPopupConfigAtom } from './store'
import xmlog from '../../../utilsV2/xmlog'



interface NotificationPopupUIProps {
  // 外部控制优先级，用于处理与其他弹窗的显示优先级
  hasPriority?: boolean
}

const NotificationPopupUI: React.FC<NotificationPopupUIProps> = ({ 
  hasPriority = true, 
}) => {
  const theme = useAtomValue(notificationPopupThemeAtom)
  const getConfig = useSetAtom(writeNotificationPopupConfigAtom)
  const styles = getStyles(theme)
  
  // 根据主题选择背景图片
  const backgroundImageUri = theme.isDark 
    ? 'https://imagev2.xmcdn.com/storages/8a69-audiofreehighqps/BF/64/GAqh1QQMHz-8AAAlQQPFlY04.png' // 暗黑模式
    : 'https://imagev2.xmcdn.com/storages/5d80-audiofreehighqps/AF/0D/GAqhfD0MHz_DAAAmdwPFlZJU.png' // 非暗黑模式
  
  // 组件内部状态管理
  const config = useAtomValue(notificationPopupConfigAtom)
  const [visible, setVisible] = useState(false)

  // 初始化和获取配置
  
  useEffect(() => {
    getConfig()
  }, [])

  // 检查并显示弹窗
  const checkAndShow = useCallback(async () => {
    if (!config || !isValidConfig(config)) {
      console.log('NotificationPopupUI: 无有效配置，跳过显示')
      return
    }

    // 开发环境下跳过每日频控检查
    const isDevelopment = __DEV__
    // const isDevelopment = true
    if (!isDevelopment) {
      // 检查今天是否已显示（仅在生产环境检查）
      const hasShown = await hasShownTodayNotificationPopup()
      if (hasShown) {
        console.log('NotificationPopupUI: 今天已显示过，跳过显示')
        return
      }
    } else {
      console.log('NotificationPopupUI: 开发环境，跳过每日频控检查')
    }

    // 检查优先级状态
    if (!hasPriority) {
      console.log('NotificationPopupUI: 优先级不足，跳过显示')
      return
    }

    console.log('NotificationPopupUI: 准备显示通知弹窗')
    setVisible(true)
  }, [config, hasPriority])




  // 优先级变化时重新检查
  useEffect(() => {
    if (config && hasPriority) {
      checkAndShow()
    }
  }, [hasPriority, config, checkAndShow])

  // 埋点：弹窗展示
  useEffect(() => {
    if (visible && config) {
      xmlog.event(68286, 'notification_popup_show', {
        currPage: 'welfareCenter',
        dialogType: '通知弹窗',
        dialogTitle: config.title
      })
    }
  }, [visible, config])

  // 关闭弹窗
  const handleClose = async () => {
    console.log('🔴 NotificationPopupUI: 关闭按钮被点击!')
    
    if (config && config.title) {
      // 埋点：弹窗关闭
      xmlog.event(68287, 'notification_popup_close', {
        currPage: 'welfareCenter',
        dialogType: '通知弹窗',
        dialogTitle: config.title,
        closeType: 'close_button'
      })
    }
    
    // 记录显示状态（仅在非强制显示模式且生产环境下记录）
    if (!__DEV__) {
      await setNotificationPopupShowRecord()
    } else if (__DEV__) {
      console.log('NotificationPopupUI: 开发环境，跳过记录显示状态')
    }
    
    console.log('NotificationPopupUI: 准备关闭弹窗')
    setVisible(false)
  }

  // 确认按钮点击
  const handleConfirm = useCallback(async () => {
    console.log('NotificationPopupUI: 确认按钮被点击')
    
    if (config && config.title) {
      // 埋点：弹窗确认
      xmlog.click(68288, 'notification_popup_confirm', {
        currPage: 'welfareCenter',
        dialogType: '通知弹窗',
        dialogTitle: config.title,
        buttonText: '我知道了'
      })
    }
    
    // 记录显示状态（仅在非强制显示模式且生产环境下记录）
    if (!__DEV__) {
      await setNotificationPopupShowRecord()
    } else if (__DEV__) {
      console.log('NotificationPopupUI: 开发环境，跳过记录显示状态')
    }
    
    console.log('NotificationPopupUI: 准备关闭弹窗')
    setVisible(false)
  }, [config])

  console.log('📱 NotificationPopupUI: 准备渲染弹窗', {
    visible,
    hasConfig: !!config,
    hasContent: !!(config?.content),
    hasTitle: !!(config?.title)
  })

  // 如果没有配置或不可见，则不渲染
  if (!visible || !config || !config.content || !config.title) {
    return null
  }

  // 解析内容中的高亮文本
  const contentParts = parseContentWithHighlight(config.content, config.color)

  return (
    <Modal visible={visible} onClose={handleClose} overlayVisible={true}>
      <View style={styles.modalContent}>
        {/* 背景图 */}
        <View style={styles.backgroundImageContainer}>
          <Image 
            source={{ uri: backgroundImageUri }}
            style={styles.backgroundImage}
            resizeMode="cover"
          />
        </View>

        {/* 铃铛图标 */}
        <View style={styles.bellIconContainer}>
          <Image 
            source={{ uri: 'https://imagev2.xmcdn.com/storages/be89-audiofreehighqps/96/1B/GAqhF9kMHzR4AAA9vgPFjHGf.png' }}
            style={styles.bellIcon}
            resizeMode="contain"
          />
        </View>

        {/* 关闭按钮 */}
        <View style={styles.closeButtonContainer}>
          <TouchableOpacity 
            style={styles.closeButton} 
            onPress={handleClose}
            activeOpacity={0.6}
          >
            <Text style={styles.closeIcon}>✕</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.contentContainer}>
          {/* 标题 */}
          <Text style={styles.title}>
            {config.title}
          </Text>

          {/* 内容 */}
          <Text style={styles.contentText}>
            {contentParts.map((part, index) => (
              <Text
                key={index}
                style={part.isHighlight ? { color: config.color } : {}}
              >
                {part.text}
              </Text>
            ))}
          </Text>

          {/* 确认按钮 */}
          <View style={styles.buttonContainer}>
            <Touch style={styles.confirmButton} onPress={handleConfirm}>
              <Text style={styles.buttonText}>我知道了</Text>
            </Touch>
          </View>
        </View>
      </View>
    </Modal>
  )
}

export default NotificationPopupUI
