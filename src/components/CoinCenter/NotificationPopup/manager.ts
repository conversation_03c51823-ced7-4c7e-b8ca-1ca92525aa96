import { getDefaultStore } from 'jotai'
import { fetchNotificationPopupConfig } from './api'
import { hasShownTodayNotificationPopup } from './storage'
import { isValidConfig } from './utils'
import {
  notificationPopupConfigAtom,
  notificationPopupVisibleAtom,
  notificationPopupInitializedAtom,
  notificationPopupPriorityAtom
} from './store'

const store = getDefaultStore()

/**
 * 通知弹窗管理器
 */
class NotificationPopupManager {
  private initialized = false

  /**
   * 初始化通知弹窗系统
   */
  async initialize() {
    if (this.initialized) {
      return
    }

    try {
      console.log('NotificationPopupManager: 开始初始化...')
      
      // 获取配置
      const response = await fetchNotificationPopupConfig()
      
      if (response?.ret === 0 && response?.data) {
        const config = response.data
        
        if (isValidConfig(config)) {
          console.log('通知弹窗配置获取成功:', config)
          store.set(notificationPopupConfigAtom, config)
          
          // 检查是否需要显示弹窗
          await this.checkAndShow()
        } else {
          console.log('通知弹窗配置无效:', config)
        }
      } else {
        console.log('通知弹窗配置获取失败:', response)
      }
      
      this.initialized = true
      store.set(notificationPopupInitializedAtom, true)
      console.log('NotificationPopupManager: 初始化完成')
    } catch (error) {
      console.log('NotificationPopupManager: 初始化失败', error)
      this.initialized = true
      store.set(notificationPopupInitializedAtom, true)
    }
  }

  /**
   * 检查并显示弹窗
   */
  async checkAndShow() {
    try {
      const config = store.get(notificationPopupConfigAtom)
      
      if (!config || !isValidConfig(config)) {
        console.log('NotificationPopupManager: 无有效配置，跳过显示')
        return
      }
      
      // 检查今天是否已显示
      const hasShown = await hasShownTodayNotificationPopup()
      if (hasShown) {
        console.log('NotificationPopupManager: 今天已显示过，跳过显示')
        return
      }
      
      // 检查优先级状态
      const hasPriority = store.get(notificationPopupPriorityAtom)
      if (!hasPriority) {
        console.log('NotificationPopupManager: 优先级不足，跳过显示')
        return
      }
      
      console.log('NotificationPopupManager: 准备显示通知弹窗')
      store.set(notificationPopupVisibleAtom, true)
    } catch (error) {
      console.log('NotificationPopupManager: 检查弹窗显示条件失败', error)
    }
  }

  /**
   * 设置优先级状态
   * 当签到弹窗等高优先级弹窗关闭后，可以显示通知弹窗
   */
  setPriority(hasPriority: boolean) {
    store.set(notificationPopupPriorityAtom, hasPriority)
    
    // 如果获得优先级且已初始化，则检查是否需要显示
    if (hasPriority && this.initialized) {
      this.checkAndShow()
    }
  }

  /**
   * 手动显示弹窗（用于测试）
   */
  async showModal() {
    try {
      const config = store.get(notificationPopupConfigAtom)
      if (config && isValidConfig(config)) {
        store.set(notificationPopupVisibleAtom, true)
      } else {
        console.log('NotificationPopupManager: 无有效配置，无法显示弹窗')
      }
    } catch (error) {
      console.log('NotificationPopupManager: 显示弹窗失败', error)
    }
  }

  /**
   * 关闭弹窗
   */
  closeModal() {
    store.set(notificationPopupVisibleAtom, false)
  }

  /**
   * 重置状态
   */
  reset() {
    store.set(notificationPopupConfigAtom, null)
    store.set(notificationPopupVisibleAtom, false)
    store.set(notificationPopupInitializedAtom, false)
    store.set(notificationPopupPriorityAtom, false)
    this.initialized = false
  }

  /**
   * 获取初始化状态
   */
  isInitialized() {
    return this.initialized
  }
}

// 导出单例
export default new NotificationPopupManager()
