import { StyleSheet } from 'react-native'
import { px } from 'utils/px';


export const getStyles = (theme: any) => StyleSheet.create({
  // 弹窗内容
  modalContent: {
    width: '100%',
    backgroundColor: theme.modalBackground,
    borderRadius: 16,
    padding: px(24),
    paddingTop: px(65), // 为铃铛图标留出空间(85/2 + 一些间距)
    alignItems: 'center',
    position: 'relative',
  },
  
  // 背景图容器
  backgroundImageContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: px(80),
    zIndex: 1,
  },
  
  // 背景图
  backgroundImage: {
    width: '100%',
    height: px(80),
    borderTopLeftRadius: 16, // 与弹窗圆角保持一致
    borderTopRightRadius: 16, // 与弹窗圆角保持一致
  },
  
  // 关闭按钮容器
  closeButtonContainer: {
    position: 'absolute',
    top: px(16),
    right: px(16),
    zIndex: 20, // 提高层级，确保在铃铛图标之上
    width: px(32), // 增大点击区域
    height: px(32), // 增大点击区域
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // 关闭按钮
  closeButton: {
    width: px(32), // 增大点击区域
    height: px(32), // 增大点击区域
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // 关闭图标
  closeIcon: {
    fontSize: px(16),
    color: theme.closeButton.iconColor,
    lineHeight: px(16),
  },
  
  // 内容容器
  contentContainer: {
    alignItems: 'flex-start',
    width: '100%',
  },
  
  // 铃铛图标容器
  bellIconContainer: {
    position: 'absolute',
    width: px(85),
    height: px(85),
    alignItems: 'center',
    justifyContent: 'center',
    top: px(-43), // 向上偏移一半高度(85/2≈43)，让一半在弹窗外
    alignSelf: 'center', // 水平居中
    zIndex: 10, // 确保图标在最上层
  },
  
  // 铃铛图标
  bellIcon: {
    width: px(85),
    height: px(85),
  },
  
  // 标题
  title: {
    fontSize: px(18),
    fontWeight: '600',
    color: theme.title.color,
    textAlign: 'center',
    lineHeight: px(25),
    marginBottom: px(16),
    width: '100%',
    alignSelf: 'center',
  },
  
  // 内容文本
  contentText: {
    fontSize: px(14),
    fontWeight: '400',
    color: theme.content.color,
    textAlign: 'left',
    lineHeight: px(22),
    marginBottom: px(32),
    paddingHorizontal: px(8),
    width: '100%',
  },
  
  // 高亮文本（用于特殊颜色的部分）
  highlightText: {
    // 动态设置颜色，从配置中获取
  },
  
  // 按钮容器
  buttonContainer: {
    width: '100%',
    alignItems: 'stretch', // 让按钮撑满容器宽度
  },
  
  // 确认按钮
  confirmButton: {
    width: '100%',
    height: px(48),
    backgroundColor: theme.button.backgroundColor,
    borderRadius: px(24),
    alignItems: 'center',
    justifyContent: 'center',
  },
  
  // 按钮文本
  buttonText: {
    fontSize: px(16),
    fontWeight: '500',
    color: theme.button.textColor,
  },
})
