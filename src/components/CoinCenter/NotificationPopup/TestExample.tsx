import React, { useState } from 'react'
import { View, Button, Text } from 'react-native'
import { useAtomValue, useSetAtom } from 'jotai'
import { themeAtom } from '../../../atom/theme'
import NotificationPopup from './index'

/**
 * NotificationPopupUI 测试示例
 * 展示简化后的组件使用方式
 */
const TestExample: React.FC = () => {
  const [hasPriority, setHasPriority] = useState(true) // 默认有优先级
  const [forceShow, setForceShow] = useState(false)
  const currentTheme = useAtomValue(themeAtom)
  const setTheme = useSetAtom(themeAtom)

  return (
    <View style={{ flex: 1, padding: 20, backgroundColor: '#f5f5f5' }}>
      <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20 }}>
        NotificationPopup 测试面板
      </Text>

      {/* 环境信息 */}
      <View style={{ 
        backgroundColor: __DEV__ ? '#e8f5e8' : '#fff3cd', 
        padding: 10, 
        borderRadius: 8,
        marginBottom: 20,
        borderWidth: 1,
        borderColor: __DEV__ ? '#d4edda' : '#ffeaa7'
      }}>
        <Text style={{ fontSize: 14, fontWeight: 'bold', color: __DEV__ ? '#155724' : '#856404' }}>
          当前环境: {__DEV__ ? '开发环境' : '生产环境'}
        </Text>
        <Text style={{ fontSize: 12, color: __DEV__ ? '#155724' : '#856404', marginTop: 5 }}>
          {__DEV__ 
            ? '✅ 已关闭每日频控，可重复测试，不记录显示状态' 
            : '⚠️ 生产环境，启用每日频控，会记录显示状态'}
        </Text>
      </View>

      <View style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 16, marginBottom: 10 }}>控制选项：</Text>
        
        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
          <Button
            title={hasPriority ? '取消优先级' : '设置优先级'}
            onPress={() => setHasPriority(!hasPriority)}
          />
          <Text style={{ marginLeft: 10 }}>
            优先级状态: {hasPriority ? '有优先级' : '无优先级'}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
          <Button
            title={forceShow ? '关闭强制' : '强制显示'}
            onPress={() => setForceShow(!forceShow)}
          />
          <Text style={{ marginLeft: 10 }}>
            强制显示: {forceShow ? '开启' : '关闭'}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
          <Button
            title={currentTheme === 'dark' ? '切换到亮色' : '切换到暗色'}
            onPress={() => setTheme(currentTheme === 'dark' ? 'light' : 'dark')}
          />
          <Text style={{ marginLeft: 10 }}>
            当前主题: {currentTheme === 'dark' ? '暗黑模式' : '亮色模式'}
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
          <Button
            title="手动显示弹窗"
            onPress={() => setForceShow(true)}
          />
          <Text style={{ marginLeft: 10, fontSize: 12, color: '#666' }}>
            点击测试关闭功能
          </Text>
        </View>

        <View style={{ flexDirection: 'row', alignItems: 'center', marginBottom: 10 }}>
          <Button
            title="关闭强制显示"
            onPress={() => setForceShow(false)}
          />
          <Text style={{ marginLeft: 10, fontSize: 12, color: '#666' }}>
            关闭后不会重新显示
          </Text>
        </View>
      </View>

      <View style={{ marginBottom: 20 }}>
        <Text style={{ fontSize: 16, marginBottom: 10 }}>使用场景：</Text>
        
        <Text style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
          • 正常使用：无需任何参数，组件自动管理
        </Text>
        <Text style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
          • 优先级控制：等签到弹窗关闭后设置 hasPriority=true
        </Text>
        <Text style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
          • 测试模式：使用 forceShow=true 强制显示
        </Text>
        <Text style={{ fontSize: 12, color: '#666', marginBottom: 5 }}>
          • 开发环境：自动跳过每日频控检查，便于重复测试
        </Text>
      </View>

      <View style={{ 
        backgroundColor: '#fff', 
        padding: 15, 
        borderRadius: 8,
        marginBottom: 20
      }}>
        <Text style={{ fontSize: 14, color: '#333' }}>当前配置:</Text>
        <Text style={{ fontSize: 12, color: '#666', marginTop: 5 }}>
          {'<NotificationPopup '}
          {hasPriority && 'hasPriority={true} '}
          {forceShow && 'forceShow={true} '}
          {'/>'}
        </Text>
      </View>

      {/* 通知弹窗组件 */}
      <NotificationPopup hasPriority={hasPriority} forceShow={forceShow} />
    </View>
  )
}

export default TestExample 