import dayjs from 'dayjs'
import storage from '../../../storageV2'
import { NotificationPopupShowRecord } from './types'

const STORAGE_KEY = 'notificationPopupShowRecord'

/**
 * 获取通知弹窗显示记录
 */
export const getNotificationPopupShowRecord = async (): Promise<NotificationPopupShowRecord | null> => {
  try {
    const res = await storage.get(STORAGE_KEY)
    if (!res || res === 'null') {
      return null
    }
    return JSON.parse(res) as NotificationPopupShowRecord
  } catch (error) {
    console.log('获取通知弹窗显示记录失败:', error)
    return null
  }
}

/**
 * 设置通知弹窗显示记录
 */
export const setNotificationPopupShowRecord = async () => {
  try {
    const today = dayjs().format('YYYY-MM-DD')
    
    const record: NotificationPopupShowRecord = {
      date: today,
      shown: true
    }
    
    await storage.set(STORAGE_KEY, record)
    console.log(`通知弹窗显示记录已更新: ${today}`)
  } catch (error) {
    console.log('设置通知弹窗显示记录失败:', error)
  }
}

/**
 * 检查今天是否已显示通知弹窗
 */
export const hasShownTodayNotificationPopup = async (): Promise<boolean> => {
  try {
    const record = await getNotificationPopupShowRecord()
    if (!record) {
      return false
    }
    
    const today = dayjs().format('YYYY-MM-DD')
    return record.date === today && record.shown
  } catch (error) {
    console.log('检查弹窗显示状态失败:', error)
    return false
  }
}

/**
 * 清除通知弹窗显示记录（用于测试）
 */
export const clearNotificationPopupShowRecord = async () => {
  try {
    await storage.remove(STORAGE_KEY)
    console.log('已清除通知弹窗显示记录')
  } catch (error) {
    console.log('清除通知弹窗显示记录失败:', error)
  }
}
