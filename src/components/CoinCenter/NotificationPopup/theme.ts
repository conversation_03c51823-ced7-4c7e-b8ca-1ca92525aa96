import { atom } from 'jotai'
import { themeAtom } from '../../../atom/theme'

// 通知弹窗主题样式
export const notificationPopupThemeAtom = atom((get) => {
  const theme = get(themeAtom)
  const isDark = theme === 'dark'
  
  return {
    // 是否为暗黑模式
    isDark,
    
    // 弹窗背景
    modalBackground: isDark ? '#2D2D2D' : '#FFFFFF',
    
    // 遮罩背景
    overlayBackground: isDark ? 'rgba(0, 0, 0, 0.8)' : 'rgba(0, 0, 0, 0.7)',
    
    // 关闭按钮
    closeButton: {
      background: isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(0, 0, 0, 0.1)',
      iconColor: isDark ? '#CCCCCC' : '#666666'
    },
    
    // 标题样式
    title: {
      fontSize: 18,
      fontWeight: '600',
      color: isDark ? '#ffffff' : '#333333',
      fontFamily: 'PingFangSC-Semibold'
    },
    
    // 内容样式
    content: {
      fontSize: 14,
      fontWeight: '400',
      color: isDark ? 'rgba(255, 255, 255, 0.55)' : 'rgba(44, 44, 60, 0.55)',
      fontFamily: 'PingFangSC-Regular'
    },
    
    // 按钮样式
    button: {
      backgroundColor: '#FF4444',
      textColor: '#FFFFFF',
      fontSize: 16,
      fontWeight: '500',
      fontFamily: 'PingFangSC-Medium'
    },
    
    // 图标
    bellIcon: require('../../../appImagesV2/icon_notification')
  }
})
