import { StyleSheet, Dimensions } from 'react-native';
import { px } from 'utils/px';

const { width } = Dimensions.get('window');

export const getStyles = (theme: any) => StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10020,
    backgroundColor: theme.overlayBackground,
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    // backgroundColor: theme.overlayBackground,
  },
  windowGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: px(16),
    padding: px(0),
    width: '100%',
    height: '20%',
    margin: px(0),
  },
  contentContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    width: width - px(60),
    maxWidth: px(320),
    backgroundColor: theme.modalBackground,
    borderRadius: px(16),
    padding: px(0),
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
}); 