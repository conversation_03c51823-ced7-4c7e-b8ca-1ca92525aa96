import React, { useEffect, useState } from 'react'
import { View, Animated, Easing, StyleSheet } from 'react-native'
import { Touch } from '@xmly/rn-components'
import LinearGradient from 'react-native-linear-gradient'
import { getStyles } from './styles'
import { useAtomValue } from 'jotai'
import { notificationPopupThemeAtom } from '../theme'

interface ModalProps {
  visible: boolean
  overlayVisible: boolean
  onClose?: () => void
  children: React.ReactNode
}

export default function Modal({ visible, overlayVisible, onClose, children }: ModalProps) {
  const theme = useAtomValue(notificationPopupThemeAtom)
  const styles = getStyles(theme)
  const [modalScale] = useState(new Animated.Value(0)) // 初始缩放为0
  const [modalOpacity] = useState(new Animated.Value(0)) // 初始透明度为0

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(modalScale, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(modalOpacity, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start()
    } else {
      modalScale.setValue(0)
      modalOpacity.setValue(0)
    }
  }, [visible, modalScale, modalOpacity])

  if (!visible) return null

  return (
    <View style={styles.container}>
      {/* 背景遮罩 - 只在需要时显示 */}
      {overlayVisible && (
        <View
          style={StyleSheet.absoluteFillObject}
          pointerEvents="auto"
        >
          <Touch
            style={styles.overlay}
            onPress={onClose}
          />
        </View>
      )}

      {/* 弹窗内容容器 */}
      <View style={styles.contentContainer} pointerEvents="box-none">
        <Animated.View
          style={[
            styles.content,
            {
              transform: [{ scale: modalScale }],
              opacity: modalOpacity,
            },
          ]}
          pointerEvents="auto"
        >
          {children}
        </Animated.View>
      </View>
    </View>
  )
}
