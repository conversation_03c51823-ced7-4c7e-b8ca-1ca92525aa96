import React, { useState } from 'react'
import { View, Text, TouchableOpacity, Alert } from 'react-native'

/**
 * 调试测试组件 - 用于验证关闭按钮功能
 */
const DebugTest: React.FC = () => {
  const [visible, setVisible] = useState(true)

  const handleClose = () => {
    console.log('🔴 DebugTest: 关闭按钮被点击!')
    Alert.alert('调试', '关闭按钮点击成功!')
    setVisible(false)
  }

  const handleReset = () => {
    setVisible(true)
  }

  if (!visible) {
    return (
      <View style={{ padding: 20, alignItems: 'center' }}>
        <Text>弹窗已关闭</Text>
        <TouchableOpacity 
          onPress={handleReset}
          style={{ marginTop: 10, padding: 10, backgroundColor: '#007AFF', borderRadius: 5 }}
        >
          <Text style={{ color: 'white' }}>重新显示</Text>
        </TouchableOpacity>
      </View>
    )
  }

  return (
    <View style={{
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: 'rgba(0,0,0,0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      zIndex: 1000
    }}>
      <View style={{
        width: 300,
        height: 200,
        backgroundColor: 'white',
        borderRadius: 16,
        padding: 20,
        position: 'relative'
      }}>
        {/* 关闭按钮 */}
        <View style={{
          position: 'absolute',
          top: 16,
          right: 16,
          zIndex: 30,
          width: 32,
          height: 32,
          alignItems: 'center',
          justifyContent: 'center',
        }}>
          <TouchableOpacity
            style={{
              width: 32,
              height: 32,
              alignItems: 'center',
              justifyContent: 'center',
              backgroundColor: 'rgba(0, 0, 0, 0.2)',
              borderRadius: 16,
            }}
            onPress={handleClose}
            activeOpacity={0.6}
          >
            <Text style={{ fontSize: 16, color: '#666' }}>✕</Text>
          </TouchableOpacity>
        </View>

        <Text style={{ fontSize: 18, fontWeight: 'bold', marginBottom: 20, marginTop: 20 }}>
          调试测试弹窗
        </Text>
        
        <Text>
          这是一个简化的测试弹窗，用于验证关闭按钮是否能正常工作。
        </Text>

        <TouchableOpacity
          style={{
            marginTop: 20,
            padding: 10,
            backgroundColor: '#FF4444',
            borderRadius: 5,
            alignItems: 'center'
          }}
          onPress={handleClose}
        >
          <Text style={{ color: 'white' }}>我知道了</Text>
        </TouchableOpacity>
      </View>
    </View>
  )
}

export default DebugTest 