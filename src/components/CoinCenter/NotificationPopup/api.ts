import { NotificationPopupResponse } from './types'

/**
 * 获取通知弹窗配置
 */
export const fetchNotificationPopupConfig = async (): Promise<NotificationPopupResponse> => {
  try {
    const url = 'https://adse.ximalaya.com/incentive/ting/welfare/queryNotifyPopup/ts-1735546969380'
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('通知弹窗配置获取成功:', data)
    
    return data
  } catch (error) {
    console.error('获取通知弹窗配置失败:', error)
    throw error
  }
}
