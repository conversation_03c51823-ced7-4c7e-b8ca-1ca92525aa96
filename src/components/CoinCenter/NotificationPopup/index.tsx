import React from 'react'

const NotificationPopupUI = React.lazy(() => import('./NotificationPopupUI'))

interface NotificationPopupProps {
  // 外部控制优先级，用于处理与其他弹窗的显示优先级
  hasPriority?: boolean
  // 是否强制显示（用于测试）
  forceShow?: boolean
}

const NotificationPopup: React.FC<NotificationPopupProps> = (props) => {
  return (
    <React.Suspense fallback={null}>
      <NotificationPopupUI {...props} />
    </React.Suspense>
  )
}

export default React.memo(NotificationPopup)
