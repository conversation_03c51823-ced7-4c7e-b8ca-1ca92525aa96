/**
 * 解析内容中的特殊标记文本
 * 将 {{文本}} 格式的内容提取出来，用于特殊颜色显示
 */
export const parseContentWithHighlight = (content: string, highlightColor: string) => {
  // 检查 content 是否为有效字符串
  if (!content || typeof content !== 'string') {
    return [{ text: '', isHighlight: false }]
  }

  // 匹配 {{}} 包围的文本
  const regex = /\{\{([^}]+)\}\}/g
  const parts: Array<{ text: string; isHighlight: boolean }> = []
  let lastIndex = 0
  let match

  while ((match = regex.exec(content)) !== null) {
    // 添加普通文本
    if (match.index > lastIndex) {
      const normalText = content.substring(lastIndex, match.index)
      if (normalText) {
        parts.push({ text: normalText, isHighlight: false })
      }
    }
    
    // 添加高亮文本
    parts.push({ text: match[1], isHighlight: true })
    lastIndex = regex.lastIndex
  }
  
  // 添加剩余的普通文本
  if (lastIndex < content.length) {
    const remainingText = content.substring(lastIndex)
    if (remainingText) {
      parts.push({ text: remainingText, isHighlight: false })
    }
  }
  
  return parts
}

/**
 * 检查配置是否有效
 */
export const isValidConfig = (config: any): boolean => {
  return (
    config &&
    config.success === true &&
    config.code === 200 &&
    typeof config.title === 'string' &&
    typeof config.content === 'string' &&
    config.title.trim() !== '' &&
    config.content.trim() !== ''
  )
}
