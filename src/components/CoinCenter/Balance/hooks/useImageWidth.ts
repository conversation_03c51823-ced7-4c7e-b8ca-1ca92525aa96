import { useState, useEffect, useMemo } from 'react';
import { Image } from 'react-native';

export const useImageWidth = (imageUri: string, height: number) => {
  const [imageSize, setImageSize] = useState<{ width: number; height: number } | null>(null);
  const [loading, setLoading] = useState(true);
  
  useEffect(() => {
    if (!imageUri) {
      setLoading(false);
      return;
    }
    
    setLoading(true);
    console.log(`开始获取图片尺寸: ${imageUri}`);
    
    Image.getSize(
      imageUri, 
      (width, originalHeight) => {
        console.log(`图片尺寸获取成功: ${imageUri}, 原始尺寸: ${width}x${originalHeight}`);
        setImageSize({ width, height: originalHeight });
        setLoading(false);
      },
      (error) => {
        console.log(`图片尺寸获取失败: ${imageUri}`, error);
        // 失败时设置为正方形
        setImageSize({ width: height, height });
        setLoading(false);
      }
    );
  }, [imageUri, height]);
  
  // 使用 useMemo 缓存计算结果
  const calculatedWidth = useMemo(() => {
    if (!imageSize) return height; // 默认正方形
    
    const aspectRatio = imageSize.width / imageSize.height;
    const finalWidth = height * aspectRatio;
    
    console.log(`计算宽度: 高度=${height}, 宽高比=${aspectRatio.toFixed(2)}, 最终宽度=${finalWidth.toFixed(2)}`);
    
    return finalWidth;
  }, [imageSize, height]);
  
  return { width: calculatedWidth, loading };
};