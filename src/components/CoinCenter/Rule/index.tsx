import React, { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import { Touch } from '@xmly/rn-components';
import { getStyles } from './style';
import { useAtomValue } from 'jotai';
import ruleThemeAtom from './theme';
import { Page } from '@xmly/rn-sdk';
import { coinCenterRuleUrl, userExperienceSurveyUrl } from 'constantsV2';
import getUrlToOpen from 'utilsV2/getUrlToOpen';
import request from 'servicesV2/request';
import { API_ADSE } from 'constantsV2/apiConfig';
import getDomainEnvSync from 'utilsV2/getEnvSync';
import xmlog from 'utilsV2/xmlog';
import getXMRequestId from 'utilsV2/getXMRequestId';

const DEFAULT_TITLE = '无法获得金币？';
const DEFAULT_URL = 'https://m.ximalaya.com/cs-bridge-web/page/feedback/knowledge-detail?knowledgeNo=-TtU7KMRjVYllHf9zGICRg&systemNum=9xX475lMiD6kDI6fxJKPSg&source=2#backPage';

export default function Rule() {
  const theme = useAtomValue(ruleThemeAtom);
  const styles = getStyles(theme);

  const [feedbackEntry, setFeedbackEntry] = useState<{
    title: string;
    url: string;
  } | null>(null);
  const [xmRequestId, setXmRequestId] = useState<string>('');

  useEffect(() => {
    getXMRequestId().then(setXmRequestId);
    const fetchFeedbackEntry = async () => {
      try {
        const ts = Date.now();
        const host = getDomainEnvSync(
          API_ADSE.test,
          API_ADSE.uat,
          API_ADSE.prod
        );
        const url = `https://${host}incentive/ting/showFeedbackEntry/ts-${ts}?type=1`;
        const res = await request({
          test: API_ADSE.test,
          uat: API_ADSE.uat,
          prod: API_ADSE.prod,
          protocol: 'https',
          url: `incentive/ting/showFeedbackEntry/ts-${ts}?type=1`,
          option: { method: 'get' },
        });
        const data = res?.data as {
          success?: boolean;
          showEntry?: boolean;
          title?: string;
          feedbackUrl?: string;
        };
        if (
          data?.success === true &&
          data?.showEntry === true
        ) {
          setFeedbackEntry({
            title: data.title && data.title.length > 0 ? data.title : DEFAULT_TITLE,
            url: data.feedbackUrl && data.feedbackUrl.length > 0 ? data.feedbackUrl : DEFAULT_URL,
          });
        }
      } catch (e) {
        // ignore error, fallback to default
      }
    };
    fetchFeedbackEntry();
  }, []);

  const handleRulesPress = () => {
    Page.start(getUrlToOpen(coinCenterRuleUrl));
  };

  const handleQuestionnairePress = () => {
    Page.start(getUrlToOpen(userExperienceSurveyUrl));
  };

  const handleFeedbackPress = () => {
    if (feedbackEntry) {
      xmlog.click(68918, undefined, {
        currPage: 'welfareCenter',
        tabName: '福利中心',
        Item: feedbackEntry.title,
        xmRequestId,
      });
      Page.start(getUrlToOpen(feedbackEntry.url));
    }
  };

  return (
    <View style={[styles.container]}>
      {feedbackEntry ? (
        <Touch onPress={handleFeedbackPress}>
          <Text style={styles.text}>{feedbackEntry.title}</Text>
        </Touch>
      ) : (
        <Touch onPress={handleQuestionnairePress}>
          <Text style={styles.text}>满意度调研</Text>
        </Touch>
      )}
      <View style={styles.line} />
      <Touch onPress={handleRulesPress}>
        <Text style={styles.text}>活动规则</Text>
      </Touch>
    </View>
  );
} 