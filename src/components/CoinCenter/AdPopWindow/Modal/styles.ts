import { StyleSheet, Dimensions } from 'react-native';
import { px } from 'utils/px';

const { width } = Dimensions.get('window');

export const getStyles = (theme: 'light' | 'dark') => StyleSheet.create({
  container: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'flex-end',
    alignItems: 'center',
    zIndex: 10010,
    padding: px(0),
    // height: px(10),
    // borderWidth: 1,
    // borderColor: 'blue',
    // borderStyle: 'dashed',
    // backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  overlayInvisible: {
    ...StyleSheet.absoluteFillObject,
    backgroundColor: 'rgba(0, 0, 0, 0)',
  },
  windowGradient: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: px(12),
    padding: px(0),
    width: '100%',
    height: '20%',
    margin: px(0),
  },
  content: {
    width: width - px(0),
    backgroundColor: theme === 'light' ? '#fff' : '#1a1a1a',
    // borderRadius: px(12),
    borderTopLeftRadius: px(12),
    borderTopRightRadius: px(12),
    padding: px(0),
    paddingTop: px(0),
    paddingBottom: px(0),
    marginBottom: px(0),
  },
}); 