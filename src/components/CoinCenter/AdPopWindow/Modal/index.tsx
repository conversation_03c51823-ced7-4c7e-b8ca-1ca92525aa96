import React, { useEffect, useState } from 'react';
import { View, Animated, Easing } from 'react-native';
import { Touch } from '@xmly/rn-components';
import LinearGradient from "react-native-linear-gradient";
import { getStyles } from './styles';
import { useAtomValue } from 'jotai';
import { themeAtom } from 'atom/theme';

interface ModalProps {
  visible: boolean;
  overlayVisible: boolean;
  onClose?: () => void;
  children: React.ReactNode;
}

export default function Modal({ visible, overlayVisible, onClose, children }: ModalProps) {
  const theme = useAtomValue(themeAtom);
  const styles = getStyles(theme);
  const [modalTranslateY] = useState(new Animated.Value(1000)); // 初始位置在屏幕下方

  useEffect(() => {
    if (visible) {
      Animated.timing(modalTranslateY, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
        easing: Easing.bezier(0.66, 0, 0.34, 1),
      }).start();
    } else {
      modalTranslateY.setValue(1000);
    }
  }, [visible, modalTranslateY]);
  if (!visible) return null;

  return (
    <View style={styles.container}>
      <Touch style={overlayVisible? styles.overlay : styles.overlayInvisible} onPress={onClose}></Touch>
        <Animated.View
          style={[
            styles.content,
            {
              transform: [{ translateY: modalTranslateY }]
            }
          ]}
        >
          <LinearGradient
              colors={['rgba(226, 160, 160, 0.2)', 'rgba(226, 160, 160, 0)']}
              style={styles.windowGradient}
            />
            
          {children}
        </Animated.View>
    </View>
  );
} 